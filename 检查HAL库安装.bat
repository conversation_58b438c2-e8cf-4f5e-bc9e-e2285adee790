@echo off
chcp 65001 >nul
echo ========================================
echo    STM32 HAL库安装检查工具
echo ========================================
echo.

echo 🔍 正在检查HAL库安装状态...
echo.

REM 检查Keil安装路径
set KEIL_PATH=C:\Keil_v5
if not exist "%KEIL_PATH%" (
    echo ❌ 未找到Keil5安装目录: %KEIL_PATH%
    echo    请检查Keil5是否正确安装
    goto :end
)
echo ✅ 找到Keil5安装目录: %KEIL_PATH%

REM 检查Pack目录
set PACK_PATH=%KEIL_PATH%\ARM\PACK\Keil
if not exist "%PACK_PATH%" (
    echo ❌ 未找到Pack目录: %PACK_PATH%
    goto :end
)
echo ✅ 找到Pack目录: %PACK_PATH%

REM 检查STM32F1xx包
echo.
echo 🔍 检查STM32F1xx Device Family Pack...
set STM32F1_FOUND=0
for /d %%i in ("%PACK_PATH%\STM32F1xx_DFP.*") do (
    set STM32F1_FOUND=1
    echo ✅ 找到STM32F1xx包: %%i
    
    REM 检查HAL库头文件
    if exist "%%i\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h" (
        echo ✅ HAL库头文件存在: stm32f1xx_hal.h
    ) else (
        echo ❌ HAL库头文件缺失: stm32f1xx_hal.h
    )
    
    REM 检查CMSIS头文件
    if exist "%%i\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h" (
        echo ✅ CMSIS头文件存在: stm32f1xx.h
    ) else (
        echo ❌ CMSIS头文件缺失: stm32f1xx.h
    )
    
    REM 检查设备头文件
    if exist "%%i\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xb.h" (
        echo ✅ 设备头文件存在: stm32f103xb.h
    ) else (
        echo ❌ 设备头文件缺失: stm32f103xb.h
    )
)

if %STM32F1_FOUND%==0 (
    echo ❌ 未找到STM32F1xx Device Family Pack
    echo.
    echo 📋 解决方案:
    echo    1. 打开Keil5
    echo    2. 按Ctrl+P打开Pack Installer
    echo    3. 选择STMicroelectronics → STM32F1 Series → STM32F103C8
    echo    4. 安装Keil::STM32F1xx_DFP包
    goto :end
)

echo.
echo ========================================
echo 📋 安装状态总结
echo ========================================
if %STM32F1_FOUND%==1 (
    echo ✅ STM32F1xx HAL库已正确安装
    echo.
    echo 🎯 下一步操作:
    echo    1. 重启Keil5
    echo    2. 重新编译您的云台项目
    echo    3. 如果仍有错误，请检查项目配置
) else (
    echo ❌ STM32F1xx HAL库未安装或安装不完整
    echo.
    echo 🔧 请按照以下步骤安装:
    echo    1. 打开Keil5
    echo    2. 按Ctrl+P打开Pack Installer
    echo    3. 选择STMicroelectronics → STM32F1 Series → STM32F103C8
    echo    4. 点击Install安装Keil::STM32F1xx_DFP包
    echo    5. 重启Keil5
)

:end
echo.
echo 按任意键退出...
pause >nul
