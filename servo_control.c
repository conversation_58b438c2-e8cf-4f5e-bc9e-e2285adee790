/* 舵机控制模块实现 */
#include "servo_control.h"

Servo_TypeDef servos[SERVO_COUNT]; // 舵机状态数组

// PWM通道映射表
uint32_t pwm_channels[SERVO_COUNT] = {
    TIM_CHANNEL_1, // 底座
    TIM_CHANNEL_2, // 肩部
    TIM_CHANNEL_3, // 肘部
    TIM_CHANNEL_4, // 腕部
    TIM_CHANNEL_1  // 夹爪(使用TIM3_CH1,需要额外配置)
};

void Servo_Init(void) {
    // 初始化舵机状态
    for(int i = 0; i < SERVO_COUNT; i++) {
        servos[i].servo_id = i;
        servos[i].current_angle = 90; // 初始角度90度
        servos[i].target_angle = 90;
        servos[i].pulse_width = SERVO_CENTER_PULSE;
        servos[i].is_moving = 0;
    }
    
    // 启动PWM输出
    HAL_TIM_PWM_Start(&htim2, TIM_CHANNEL_1);
    HAL_TIM_PWM_Start(&htim2, TIM_CHANNEL_2);
    HAL_TIM_PWM_Start(&htim2, TIM_CHANNEL_3);
    HAL_TIM_PWM_Start(&htim2, TIM_CHANNEL_4);
    
    // 设置初始脉宽
    for(int i = 0; i < 4; i++) { // 前4个舵机使用TIM2
        __HAL_TIM_SET_COMPARE(&htim2, pwm_channels[i], SERVO_CENTER_PULSE);
    }
}

uint16_t Angle_To_Pulse(uint16_t angle) {
    if(angle > SERVO_MAX_ANGLE) angle = SERVO_MAX_ANGLE;
    if(angle < SERVO_MIN_ANGLE) angle = SERVO_MIN_ANGLE;
    
    // 线性映射: 0-180度 -> 500-2500us
    return SERVO_MIN_PULSE + (angle * (SERVO_MAX_PULSE - SERVO_MIN_PULSE)) / SERVO_MAX_ANGLE;
}

uint16_t Pulse_To_Angle(uint16_t pulse) {
    if(pulse > SERVO_MAX_PULSE) pulse = SERVO_MAX_PULSE;
    if(pulse < SERVO_MIN_PULSE) pulse = SERVO_MIN_PULSE;
    
    return ((pulse - SERVO_MIN_PULSE) * SERVO_MAX_ANGLE) / (SERVO_MAX_PULSE - SERVO_MIN_PULSE);
}

void Servo_SetAngle(uint8_t servo_id, uint16_t angle) {
    if(servo_id >= SERVO_COUNT) return;
    
    uint16_t pulse = Angle_To_Pulse(angle);
    servos[servo_id].current_angle = angle;
    servos[servo_id].target_angle = angle;
    servos[servo_id].pulse_width = pulse;
    
    if(servo_id < 4) { // 前4个舵机使用TIM2
        __HAL_TIM_SET_COMPARE(&htim2, pwm_channels[servo_id], pulse);
    }
}

void Servo_SetPulse(uint8_t servo_id, uint16_t pulse) {
    if(servo_id >= SERVO_COUNT) return;
    
    servos[servo_id].pulse_width = pulse;
    servos[servo_id].current_angle = Pulse_To_Angle(pulse);
    
    if(servo_id < 4) {
        __HAL_TIM_SET_COMPARE(&htim2, pwm_channels[servo_id], pulse);
    }
}

uint16_t Servo_GetAngle(uint8_t servo_id) {
    if(servo_id >= SERVO_COUNT) return 0;
    return servos[servo_id].current_angle;
}

void Servo_SmoothMove(uint8_t servo_id, uint16_t target_angle, uint8_t speed) {
    if(servo_id >= SERVO_COUNT) return;
    
    servos[servo_id].target_angle = target_angle;
    servos[servo_id].is_moving = 1;
    
    uint16_t current = servos[servo_id].current_angle;
    
    while(current != target_angle && servos[servo_id].is_moving) {
        if(current < target_angle) {
            current += (current + speed <= target_angle) ? speed : (target_angle - current);
        } else {
            current -= (current - speed >= target_angle) ? speed : (current - target_angle);
        }
        
        Servo_SetAngle(servo_id, current);
        HAL_Delay(MOVE_DELAY_MS);
    }
    
    servos[servo_id].is_moving = 0;
}

void Servo_Stop(uint8_t servo_id) {
    if(servo_id >= SERVO_COUNT) return;
    servos[servo_id].is_moving = 0;
}

void Servo_Home(void) {
    // 平滑移动到初始位置
    Servo_SmoothMove(SERVO_BASE, POS_HOME_BASE, MOVE_SPEED_NORMAL);
    Servo_SmoothMove(SERVO_SHOULDER, POS_HOME_SHOULDER, MOVE_SPEED_NORMAL);
    Servo_SmoothMove(SERVO_ELBOW, POS_HOME_ELBOW, MOVE_SPEED_NORMAL);
    Servo_SmoothMove(SERVO_WRIST, POS_HOME_WRIST, MOVE_SPEED_NORMAL);
    Servo_SmoothMove(SERVO_GRIPPER, POS_HOME_GRIPPER, MOVE_SPEED_NORMAL);
}

void Move_To_Position(uint16_t base, uint16_t shoulder, uint16_t elbow, uint16_t wrist, uint16_t gripper) {
    // 同时移动所有关节到指定位置
    servos[SERVO_BASE].target_angle = base;
    servos[SERVO_SHOULDER].target_angle = shoulder;
    servos[SERVO_ELBOW].target_angle = elbow;
    servos[SERVO_WRIST].target_angle = wrist;
    servos[SERVO_GRIPPER].target_angle = gripper;
    
    uint8_t all_reached = 0;
    while(!all_reached) {
        all_reached = 1;
        
        for(int i = 0; i < SERVO_COUNT; i++) {
            uint16_t current = servos[i].current_angle;
            uint16_t target = servos[i].target_angle;
            
            if(current != target) {
                all_reached = 0;
                if(current < target) {
                    current += (current + MOVE_SPEED_NORMAL <= target) ? MOVE_SPEED_NORMAL : (target - current);
                } else {
                    current -= (current - MOVE_SPEED_NORMAL >= target) ? MOVE_SPEED_NORMAL : (current - target);
                }
                Servo_SetAngle(i, current);
            }
        }
        HAL_Delay(MOVE_DELAY_MS);
    }
}

void Process_Command(uint8_t cmd) {
    char response[50];
    
    switch(cmd) {
        case CMD_STOP:
            for(int i = 0; i < SERVO_COUNT; i++) Servo_Stop(i);
            sprintf(response, "机械臂停止\r\n");
            break;
            
        case CMD_HOME:
            Servo_Home();
            sprintf(response, "回到初始位置\r\n");
            break;
            
        case CMD_DEMO:
            Execute_Demo_Sequence();
            sprintf(response, "演示动作完成\r\n");
            break;
            
        case CMD_BASE_LEFT:
            Servo_SmoothMove(SERVO_BASE, Servo_GetAngle(SERVO_BASE) - MOVE_SPEED_NORMAL, MOVE_SPEED_FAST);
            sprintf(response, "底座左转\r\n");
            break;
            
        case CMD_BASE_RIGHT:
            Servo_SmoothMove(SERVO_BASE, Servo_GetAngle(SERVO_BASE) + MOVE_SPEED_NORMAL, MOVE_SPEED_FAST);
            sprintf(response, "底座右转\r\n");
            break;
            
        case CMD_SHOULDER_UP:
            Servo_SmoothMove(SERVO_SHOULDER, Servo_GetAngle(SERVO_SHOULDER) + MOVE_SPEED_NORMAL, MOVE_SPEED_FAST);
            sprintf(response, "肩部上抬\r\n");
            break;
            
        case CMD_SHOULDER_DOWN:
            Servo_SmoothMove(SERVO_SHOULDER, Servo_GetAngle(SERVO_SHOULDER) - MOVE_SPEED_NORMAL, MOVE_SPEED_FAST);
            sprintf(response, "肩部下压\r\n");
            break;
            
        case CMD_ELBOW_UP:
            Servo_SmoothMove(SERVO_ELBOW, Servo_GetAngle(SERVO_ELBOW) + MOVE_SPEED_NORMAL, MOVE_SPEED_FAST);
            sprintf(response, "肘部上抬\r\n");
            break;
            
        case CMD_ELBOW_DOWN:
            Servo_SmoothMove(SERVO_ELBOW, Servo_GetAngle(SERVO_ELBOW) - MOVE_SPEED_NORMAL, MOVE_SPEED_FAST);
            sprintf(response, "肘部下压\r\n");
            break;
            
        case CMD_WRIST_UP:
            Servo_SmoothMove(SERVO_WRIST, Servo_GetAngle(SERVO_WRIST) + MOVE_SPEED_NORMAL, MOVE_SPEED_FAST);
            sprintf(response, "腕部上抬\r\n");
            break;
            
        case CMD_WRIST_DOWN:
            Servo_SmoothMove(SERVO_WRIST, Servo_GetAngle(SERVO_WRIST) - MOVE_SPEED_NORMAL, MOVE_SPEED_FAST);
            sprintf(response, "腕部下压\r\n");
            break;
            
        case CMD_GRIPPER_OPEN:
            Servo_SmoothMove(SERVO_GRIPPER, POS_PLACE_GRIPPER, MOVE_SPEED_NORMAL);
            sprintf(response, "夹爪张开\r\n");
            break;
            
        case CMD_GRIPPER_CLOSE:
            Servo_SmoothMove(SERVO_GRIPPER, POS_PICK_GRIPPER, MOVE_SPEED_NORMAL);
            sprintf(response, "夹爪闭合\r\n");
            break;
            
        default:
            sprintf(response, "未知命令: %c\r\n", cmd);
            break;
    }
    
    HAL_UART_Transmit(&huart1, (uint8_t*)response, strlen(response), HAL_MAX_DELAY);
}

void Execute_Demo_Sequence(void) {
    char msg[] = "开始演示动作序列\r\n";
    HAL_UART_Transmit(&huart1, (uint8_t*)msg, strlen(msg), HAL_MAX_DELAY);
    
    // 1. 回到初始位置
    Move_To_Position(POS_HOME_BASE, POS_HOME_SHOULDER, POS_HOME_ELBOW, POS_HOME_WRIST, POS_HOME_GRIPPER);
    HAL_Delay(1000);
    
    // 2. 移动到抓取位置
    Move_To_Position(POS_PICK_BASE, POS_PICK_SHOULDER, POS_PICK_ELBOW, POS_PICK_WRIST, POS_PICK_GRIPPER);
    HAL_Delay(1000);
    
    // 3. 夹爪闭合
    Servo_SmoothMove(SERVO_GRIPPER, POS_PICK_GRIPPER, MOVE_SPEED_SLOW);
    HAL_Delay(500);
    
    // 4. 抬起物体
    Servo_SmoothMove(SERVO_SHOULDER, POS_HOME_SHOULDER, MOVE_SPEED_NORMAL);
    HAL_Delay(1000);
    
    // 5. 移动到放置位置
    Move_To_Position(POS_PLACE_BASE, POS_PLACE_SHOULDER, POS_PLACE_ELBOW, POS_PLACE_WRIST, POS_PICK_GRIPPER);
    HAL_Delay(1000);
    
    // 6. 夹爪张开
    Servo_SmoothMove(SERVO_GRIPPER, POS_PLACE_GRIPPER, MOVE_SPEED_SLOW);
    HAL_Delay(500);
    
    // 7. 回到初始位置
    Move_To_Position(POS_HOME_BASE, POS_HOME_SHOULDER, POS_HOME_ELBOW, POS_HOME_WRIST, POS_HOME_GRIPPER);
    
    char complete_msg[] = "演示动作序列完成\r\n";
    HAL_UART_Transmit(&huart1, (uint8_t*)complete_msg, strlen(complete_msg), HAL_MAX_DELAY);
}
