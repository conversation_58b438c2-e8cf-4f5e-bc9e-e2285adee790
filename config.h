/* 云台控制系统配置文件 */
#ifndef __CONFIG_H
#define __CONFIG_H

// 系统配置
#define SYSTEM_CLOCK_FREQ 72000000 // 系统时钟频率72MHz
#define PWM_FREQUENCY 50 // PWM频率50Hz
#define PWM_PERIOD 20000 // PWM周期20ms(单位:us)

// 舵机配置
#define SERVO_COUNT 2 // 舵机数量(偏航+俯仰)
#define SERVO_MIN_PULSE 500 // 最小脉宽0.5ms
#define SERVO_MAX_PULSE 2500 // 最大脉宽2.5ms
#define SERVO_CENTER_PULSE 1500 // 中心脉宽1.5ms

// 云台轴定义
#define GIMBAL_YAW 0 // 偏航轴(水平旋转)
#define GIMBAL_PITCH 1 // 俯仰轴(垂直旋转)

// 舵机角度限制
#define SERVO_MIN_ANGLE 0 // 最小角度
#define SERVO_MAX_ANGLE 180 // 最大角度

// 云台角度限制
#define YAW_MIN_ANGLE 0 // 偏航最小角度
#define YAW_MAX_ANGLE 180 // 偏航最大角度
#define PITCH_MIN_ANGLE 30 // 俯仰最小角度(防止碰撞)
#define PITCH_MAX_ANGLE 150 // 俯仰最大角度

// 串口配置
#define UART_BAUDRATE 115200 // 波特率
#define RX_BUFFER_SIZE 64 // 接收缓冲区大小
#define TX_BUFFER_SIZE 128 // 发送缓冲区大小

// 运动控制配置
#define MOVE_SPEED_SLOW 2 // 慢速移动步长
#define MOVE_SPEED_NORMAL 5 // 正常移动步长
#define MOVE_SPEED_FAST 10 // 快速移动步长
#define MOVE_DELAY_MS 30 // 移动间隔时间

// GPIO配置
#define LED_PIN GPIO_PIN_13 // LED指示灯引脚
#define LED_PORT GPIOC // LED端口
#define BUTTON_PIN GPIO_PIN_12 // 按键引脚
#define BUTTON_PORT GPIOB // 按键端口

// PWM通道配置
#define PWM_CHANNEL_YAW TIM_CHANNEL_1 // 偏航轴PWM通道
#define PWM_CHANNEL_PITCH TIM_CHANNEL_2 // 俯仰轴PWM通道

// 命令定义
#define CMD_STOP 'S' // 停止命令
#define CMD_HOME 'H' // 回到初始位置
#define CMD_DEMO 'D' // 演示动作
#define CMD_YAW_LEFT 'A' // 偏航左转
#define CMD_YAW_RIGHT 'D' // 偏航右转
#define CMD_PITCH_UP 'W' // 俯仰向上
#define CMD_PITCH_DOWN 'S' // 俯仰向下
#define CMD_TRACK_MODE 'T' // 跟踪模式
#define CMD_PATROL_MODE 'P' // 巡航模式

// 预设位置定义
#define POS_HOME_YAW 90 // 初始位置-偏航
#define POS_HOME_PITCH 90 // 初始位置-俯仰

#define POS_LOOK_LEFT_YAW 45 // 左视位置-偏航
#define POS_LOOK_LEFT_PITCH 90 // 左视位置-俯仰

#define POS_LOOK_RIGHT_YAW 135 // 右视位置-偏航
#define POS_LOOK_RIGHT_PITCH 90 // 右视位置-俯仰

#define POS_LOOK_UP_YAW 90 // 上视位置-偏航
#define POS_LOOK_UP_PITCH 60 // 上视位置-俯仰

#define POS_LOOK_DOWN_YAW 90 // 下视位置-偏航
#define POS_LOOK_DOWN_PITCH 120 // 下视位置-俯仰

// 跟踪模式配置
#define TRACK_SPEED 3 // 跟踪速度
#define PATROL_SPEED 2 // 巡航速度
#define PATROL_DELAY 2000 // 巡航停留时间ms

#endif /* __CONFIG_H */
