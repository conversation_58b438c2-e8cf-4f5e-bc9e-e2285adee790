/* 机械臂控制系统配置文件 */
#ifndef __CONFIG_H
#define __CONFIG_H

// 系统配置
#define SYSTEM_CLOCK_FREQ 72000000 // 系统时钟频率72MHz
#define PWM_FREQUENCY 50 // PWM频率50Hz
#define PWM_PERIOD 20000 // PWM周期20ms(单位:us)

// 舵机配置
#define SERVO_COUNT 5 // 舵机数量
#define SERVO_MIN_PULSE 500 // 最小脉宽0.5ms
#define SERVO_MAX_PULSE 2500 // 最大脉宽2.5ms
#define SERVO_CENTER_PULSE 1500 // 中心脉宽1.5ms

// 舵机编号定义
#define SERVO_BASE 0 // 底座旋转
#define SERVO_SHOULDER 1 // 肩部关节
#define SERVO_ELBOW 2 // 肘部关节
#define SERVO_WRIST 3 // 腕部关节
#define SERVO_GRIPPER 4 // 夹爪

// 舵机角度限制
#define SERVO_MIN_ANGLE 0 // 最小角度
#define SERVO_MAX_ANGLE 180 // 最大角度

// 串口配置
#define UART_BAUDRATE 115200 // 波特率
#define RX_BUFFER_SIZE 64 // 接收缓冲区大小
#define TX_BUFFER_SIZE 128 // 发送缓冲区大小

// 运动控制配置
#define MOVE_SPEED_SLOW 5 // 慢速移动步长
#define MOVE_SPEED_NORMAL 10 // 正常移动步长
#define MOVE_SPEED_FAST 20 // 快速移动步长
#define MOVE_DELAY_MS 50 // 移动间隔时间

// GPIO配置
#define LED_PIN GPIO_PIN_13 // LED指示灯引脚
#define LED_PORT GPIOC // LED端口
#define BUTTON_PIN GPIO_PIN_12 // 按键引脚
#define BUTTON_PORT GPIOB // 按键端口

// PWM通道配置
#define PWM_CHANNEL_BASE TIM_CHANNEL_1 // 底座PWM通道
#define PWM_CHANNEL_SHOULDER TIM_CHANNEL_2 // 肩部PWM通道
#define PWM_CHANNEL_ELBOW TIM_CHANNEL_3 // 肘部PWM通道
#define PWM_CHANNEL_WRIST TIM_CHANNEL_4 // 腕部PWM通道

// 命令定义
#define CMD_STOP 'S' // 停止命令
#define CMD_HOME 'H' // 回到初始位置
#define CMD_DEMO 'D' // 演示动作
#define CMD_BASE_LEFT 'Q' // 底座左转
#define CMD_BASE_RIGHT 'E' // 底座右转
#define CMD_SHOULDER_UP 'W' // 肩部上抬
#define CMD_SHOULDER_DOWN 'X' // 肩部下压
#define CMD_ELBOW_UP 'A' // 肘部上抬
#define CMD_ELBOW_DOWN 'Z' // 肘部下压
#define CMD_WRIST_UP 'R' // 腕部上抬
#define CMD_WRIST_DOWN 'F' // 腕部下压
#define CMD_GRIPPER_OPEN 'T' // 夹爪张开
#define CMD_GRIPPER_CLOSE 'G' // 夹爪闭合

// 预设位置定义
#define POS_HOME_BASE 90 // 初始位置-底座
#define POS_HOME_SHOULDER 90 // 初始位置-肩部
#define POS_HOME_ELBOW 90 // 初始位置-肘部
#define POS_HOME_WRIST 90 // 初始位置-腕部
#define POS_HOME_GRIPPER 90 // 初始位置-夹爪

#define POS_PICK_BASE 45 // 抓取位置-底座
#define POS_PICK_SHOULDER 60 // 抓取位置-肩部
#define POS_PICK_ELBOW 120 // 抓取位置-肘部
#define POS_PICK_WRIST 90 // 抓取位置-腕部
#define POS_PICK_GRIPPER 30 // 抓取位置-夹爪

#define POS_PLACE_BASE 135 // 放置位置-底座
#define POS_PLACE_SHOULDER 60 // 放置位置-肩部
#define POS_PLACE_ELBOW 120 // 放置位置-肘部
#define POS_PLACE_WRIST 90 // 放置位置-腕部
#define POS_PLACE_GRIPPER 150 // 放置位置-夹爪

#endif /* __CONFIG_H */
