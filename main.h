/* STM32C8T6云台控制系统头文件 */
#ifndef __MAIN_H
#define __MAIN_H

#ifdef __cplusplus
extern "C" {
#endif

// 如果HAL库未安装，请先安装STM32F1xx Device Family Pack
// 或者使用标准库版本 main_stdlib.c
#ifdef USE_HAL_DRIVER
#include "stm32f1xx_hal.h"
#else
#include "stm32f10x.h"
#endif
#include <string.h>
#include <stdio.h>

// 错误处理函数
void Error_Handler(void);

// 外部变量声明
extern TIM_HandleTypeDef htim2;
extern UART_HandleTypeDef huart1;

#ifdef __cplusplus
}
#endif

#endif /* __MAIN_H */
