/* STM32C8T6云台控制系统主程序 - 标准库版本 */
#include "stm32f10x.h"
#include <stdio.h>
#include <string.h>

// 系统配置
#define SYSTEM_CLOCK_FREQ 72000000
#define PWM_FREQUENCY 50
#define PWM_PERIOD 20000

// 舵机配置
#define SERVO_COUNT 2
#define SERVO_MIN_PULSE 500
#define SERVO_MAX_PULSE 2500
#define SERVO_CENTER_PULSE 1500

// 云台轴定义
#define GIMBAL_YAW 0
#define GIMBAL_PITCH 1

// 角度限制
#define SERVO_MIN_ANGLE 0
#define SERVO_MAX_ANGLE 180
#define YAW_MIN_ANGLE 0
#define YAW_MAX_ANGLE 180
#define PITCH_MIN_ANGLE 30
#define PITCH_MAX_ANGLE 150

// 运动控制配置
#define MOVE_SPEED_SLOW 2
#define MOVE_SPEED_NORMAL 5
#define MOVE_SPEED_FAST 10
#define MOVE_DELAY_MS 30

// 命令定义
#define CMD_STOP 'S'
#define CMD_HOME 'H'
#define CMD_DEMO 'D'
#define CMD_YAW_LEFT 'A'
#define CMD_YAW_RIGHT 'E'
#define CMD_PITCH_UP 'W'
#define CMD_PITCH_DOWN 'X'
#define CMD_TRACK_MODE 'T'
#define CMD_PATROL_MODE 'P'

// 预设位置定义
#define POS_HOME_YAW 90
#define POS_HOME_PITCH 90
#define POS_LOOK_LEFT_YAW 45
#define POS_LOOK_LEFT_PITCH 90
#define POS_LOOK_RIGHT_YAW 135
#define POS_LOOK_RIGHT_PITCH 90
#define POS_LOOK_UP_YAW 90
#define POS_LOOK_UP_PITCH 60
#define POS_LOOK_DOWN_YAW 90
#define POS_LOOK_DOWN_PITCH 120

// 云台状态结构体
typedef struct {
    uint8_t axis_id;
    uint16_t current_angle;
    uint16_t target_angle;
    uint16_t pulse_width;
    uint8_t is_moving;
} Gimbal_TypeDef;

// 云台模式枚举
typedef enum {
    GIMBAL_MODE_MANUAL = 0,
    GIMBAL_MODE_TRACK,
    GIMBAL_MODE_PATROL
} Gimbal_Mode_t;

// 全局变量
Gimbal_TypeDef gimbal[SERVO_COUNT];
Gimbal_Mode_t gimbal_mode = GIMBAL_MODE_MANUAL;

// 函数声明
void SystemInit_Config(void);
void GPIO_Config(void);
void TIM2_Config(void);
void USART1_Config(void);
void Delay_ms(uint32_t ms);
void Gimbal_Init(void);
void Gimbal_SetAngle(uint8_t axis_id, uint16_t angle);
uint16_t Angle_To_Pulse(uint16_t angle);
void Process_Command(uint8_t cmd);
void Execute_Demo_Sequence(void);
void USART1_SendString(char* str);

int main(void) {
    SystemInit_Config(); // 系统初始化
    GPIO_Config(); // GPIO配置
    TIM2_Config(); // 定时器配置
    USART1_Config(); // 串口配置
    
    Gimbal_Init(); // 云台初始化
    
    USART1_SendString("STM32云台控制系统启动\r\n");
    
    // 云台初始位置
    Gimbal_SetAngle(GIMBAL_YAW, 90);
    Gimbal_SetAngle(GIMBAL_PITCH, 90);
    
    while (1) {
        // 简单的主循环
        if (USART1->SR & USART_SR_RXNE) {
            uint8_t cmd = USART1->DR;
            Process_Command(cmd);
        }
        
        // 检查按键
        if (!(GPIOB->IDR & GPIO_Pin_12)) {
            Execute_Demo_Sequence();
            Delay_ms(1000);
        }
        
        Delay_ms(10);
    }
}

void SystemInit_Config(void) {
    // 使用外部8MHz晶振，PLL倍频到72MHz
    RCC->CR |= RCC_CR_HSEON; // 使能外部高速时钟
    while(!(RCC->CR & RCC_CR_HSERDY)); // 等待外部时钟稳定
    
    RCC->CFGR |= RCC_CFGR_PLLSRC; // PLL时钟源选择HSE
    RCC->CFGR |= RCC_CFGR_PLLMULL9; // PLL倍频系数9 (8MHz * 9 = 72MHz)
    
    RCC->CR |= RCC_CR_PLLON; // 使能PLL
    while(!(RCC->CR & RCC_CR_PLLRDY)); // 等待PLL稳定
    
    RCC->CFGR |= RCC_CFGR_SW_PLL; // 系统时钟选择PLL
    while((RCC->CFGR & RCC_CFGR_SWS) != RCC_CFGR_SWS_PLL); // 等待切换完成
}

void GPIO_Config(void) {
    // 使能时钟
    RCC->APB2ENR |= RCC_APB2ENR_IOPAEN | RCC_APB2ENR_IOPBEN | RCC_APB2ENR_IOPCEN;
    
    // PA0,PA1配置为复用推挽输出(TIM2_CH1,TIM2_CH2)
    GPIOA->CRL &= ~(GPIO_CRL_CNF0 | GPIO_CRL_MODE0);
    GPIOA->CRL |= GPIO_CRL_CNF0_1 | GPIO_CRL_MODE0;
    GPIOA->CRL &= ~(GPIO_CRL_CNF1 | GPIO_CRL_MODE1);
    GPIOA->CRL |= GPIO_CRL_CNF1_1 | GPIO_CRL_MODE1;
    
    // PA9配置为复用推挽输出(USART1_TX)
    GPIOA->CRH &= ~(GPIO_CRH_CNF9 | GPIO_CRH_MODE9);
    GPIOA->CRH |= GPIO_CRH_CNF9_1 | GPIO_CRH_MODE9;
    
    // PA10配置为浮空输入(USART1_RX)
    GPIOA->CRH &= ~(GPIO_CRH_CNF10 | GPIO_CRH_MODE10);
    GPIOA->CRH |= GPIO_CRH_CNF10_0;
    
    // PC13配置为推挽输出(LED)
    GPIOC->CRH &= ~(GPIO_CRH_CNF13 | GPIO_CRH_MODE13);
    GPIOC->CRH |= GPIO_CRH_MODE13_0;
    
    // PB12配置为上拉输入(按键)
    GPIOB->CRH &= ~(GPIO_CRH_CNF12 | GPIO_CRH_MODE12);
    GPIOB->CRH |= GPIO_CRH_CNF12_1;
    GPIOB->ODR |= GPIO_Pin_12;
}

void TIM2_Config(void) {
    // 使能TIM2时钟
    RCC->APB1ENR |= RCC_APB1ENR_TIM2EN;
    
    // 配置TIM2为PWM模式
    TIM2->PSC = 71; // 预分频器 72MHz/72=1MHz
    TIM2->ARR = 19999; // 自动重装载值 20ms周期
    
    // 配置CH1和CH2为PWM模式1
    TIM2->CCMR1 |= TIM_CCMR1_OC1M_2 | TIM_CCMR1_OC1M_1 | TIM_CCMR1_OC1PE;
    TIM2->CCMR1 |= TIM_CCMR1_OC2M_2 | TIM_CCMR1_OC2M_1 | TIM_CCMR1_OC2PE;
    
    // 使能输出
    TIM2->CCER |= TIM_CCER_CC1E | TIM_CCER_CC2E;
    
    // 设置初始脉宽
    TIM2->CCR1 = SERVO_CENTER_PULSE;
    TIM2->CCR2 = SERVO_CENTER_PULSE;
    
    // 使能定时器
    TIM2->CR1 |= TIM_CR1_CEN;
}

void USART1_Config(void) {
    // 使能USART1时钟
    RCC->APB2ENR |= RCC_APB2ENR_USART1EN;
    
    // 配置波特率115200
    USART1->BRR = 0x271; // 72MHz/115200
    
    // 使能发送和接收
    USART1->CR1 |= USART_CR1_TE | USART_CR1_RE | USART_CR1_UE;
}

void Delay_ms(uint32_t ms) {
    uint32_t i, j;
    for(i = 0; i < ms; i++) {
        for(j = 0; j < 7200; j++); // 大约1ms延时
    }
}

void Gimbal_Init(void) {
    for(int i = 0; i < SERVO_COUNT; i++) {
        gimbal[i].axis_id = i;
        gimbal[i].current_angle = 90;
        gimbal[i].target_angle = 90;
        gimbal[i].pulse_width = SERVO_CENTER_PULSE;
        gimbal[i].is_moving = 0;
    }
    gimbal_mode = GIMBAL_MODE_MANUAL;
}

uint16_t Angle_To_Pulse(uint16_t angle) {
    if(angle > SERVO_MAX_ANGLE) angle = SERVO_MAX_ANGLE;
    if(angle < SERVO_MIN_ANGLE) angle = SERVO_MIN_ANGLE;
    return SERVO_MIN_PULSE + (angle * (SERVO_MAX_PULSE - SERVO_MIN_PULSE)) / SERVO_MAX_ANGLE;
}

void Gimbal_SetAngle(uint8_t axis_id, uint16_t angle) {
    if(axis_id >= SERVO_COUNT) return;
    
    // 角度限制检查
    if(axis_id == GIMBAL_YAW) {
        if(angle < YAW_MIN_ANGLE) angle = YAW_MIN_ANGLE;
        if(angle > YAW_MAX_ANGLE) angle = YAW_MAX_ANGLE;
    } else if(axis_id == GIMBAL_PITCH) {
        if(angle < PITCH_MIN_ANGLE) angle = PITCH_MIN_ANGLE;
        if(angle > PITCH_MAX_ANGLE) angle = PITCH_MAX_ANGLE;
    }
    
    uint16_t pulse = Angle_To_Pulse(angle);
    gimbal[axis_id].current_angle = angle;
    gimbal[axis_id].pulse_width = pulse;
    
    if(axis_id == GIMBAL_YAW) {
        TIM2->CCR1 = pulse;
    } else if(axis_id == GIMBAL_PITCH) {
        TIM2->CCR2 = pulse;
    }
}

void USART1_SendString(char* str) {
    while(*str) {
        while(!(USART1->SR & USART_SR_TXE));
        USART1->DR = *str++;
    }
}

void Process_Command(uint8_t cmd) {
    switch(cmd) {
        case CMD_STOP:
            gimbal_mode = GIMBAL_MODE_MANUAL;
            USART1_SendString("云台停止\r\n");
            break;
        case CMD_HOME:
            Gimbal_SetAngle(GIMBAL_YAW, POS_HOME_YAW);
            Gimbal_SetAngle(GIMBAL_PITCH, POS_HOME_PITCH);
            USART1_SendString("回到初始位置\r\n");
            break;
        case CMD_DEMO:
            Execute_Demo_Sequence();
            USART1_SendString("演示完成\r\n");
            break;
        case CMD_YAW_LEFT:
            if(gimbal[GIMBAL_YAW].current_angle > YAW_MIN_ANGLE + MOVE_SPEED_FAST) {
                Gimbal_SetAngle(GIMBAL_YAW, gimbal[GIMBAL_YAW].current_angle - MOVE_SPEED_FAST);
            }
            USART1_SendString("偏航左转\r\n");
            break;
        case CMD_YAW_RIGHT:
            if(gimbal[GIMBAL_YAW].current_angle < YAW_MAX_ANGLE - MOVE_SPEED_FAST) {
                Gimbal_SetAngle(GIMBAL_YAW, gimbal[GIMBAL_YAW].current_angle + MOVE_SPEED_FAST);
            }
            USART1_SendString("偏航右转\r\n");
            break;
        case CMD_PITCH_UP:
            if(gimbal[GIMBAL_PITCH].current_angle > PITCH_MIN_ANGLE + MOVE_SPEED_FAST) {
                Gimbal_SetAngle(GIMBAL_PITCH, gimbal[GIMBAL_PITCH].current_angle - MOVE_SPEED_FAST);
            }
            USART1_SendString("俯仰向上\r\n");
            break;
        case CMD_PITCH_DOWN:
            if(gimbal[GIMBAL_PITCH].current_angle < PITCH_MAX_ANGLE - MOVE_SPEED_FAST) {
                Gimbal_SetAngle(GIMBAL_PITCH, gimbal[GIMBAL_PITCH].current_angle + MOVE_SPEED_FAST);
            }
            USART1_SendString("俯仰向下\r\n");
            break;
        default:
            USART1_SendString("未知命令\r\n");
            break;
    }
}

void Execute_Demo_Sequence(void) {
    USART1_SendString("开始演示\r\n");
    
    // 演示序列
    Gimbal_SetAngle(GIMBAL_YAW, POS_HOME_YAW);
    Gimbal_SetAngle(GIMBAL_PITCH, POS_HOME_PITCH);
    Delay_ms(1000);
    
    Gimbal_SetAngle(GIMBAL_YAW, POS_LOOK_LEFT_YAW);
    Delay_ms(1000);
    
    Gimbal_SetAngle(GIMBAL_YAW, POS_LOOK_RIGHT_YAW);
    Delay_ms(1000);
    
    Gimbal_SetAngle(GIMBAL_YAW, POS_HOME_YAW);
    Gimbal_SetAngle(GIMBAL_PITCH, POS_LOOK_UP_PITCH);
    Delay_ms(1000);
    
    Gimbal_SetAngle(GIMBAL_PITCH, POS_LOOK_DOWN_PITCH);
    Delay_ms(1000);
    
    Gimbal_SetAngle(GIMBAL_YAW, POS_HOME_YAW);
    Gimbal_SetAngle(GIMBAL_PITCH, POS_HOME_PITCH);
}
