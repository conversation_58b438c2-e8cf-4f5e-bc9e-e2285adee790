/* 系统初始化文件 */
#include "stm32f1xx.h"

#if !defined  (HSE_VALUE) 
#define HSE_VALUE    8000000U // 外部高速振荡器频率值
#endif

#if !defined  (HSI_VALUE)
#define HSI_VALUE    8000000U // 内部高速振荡器频率值
#endif

uint32_t SystemCoreClock = 72000000; // 系统时钟频率
const uint8_t AHBPrescTable[16U] = {0, 0, 0, 0, 0, 0, 0, 0, 1, 2, 3, 4, 6, 7, 8, 9}; // AHB预分频表
const uint8_t APBPrescTable[8U] =  {0, 0, 0, 0, 1, 2, 3, 4}; // APB预分频表

void SystemInit(void) {
    // 复位RCC时钟配置为默认复位状态
    RCC->CR |= 0x00000001U; // 设置HSION位
    
    // 复位SW, HPRE, PPRE1, PPRE2, ADCPRE和MCO位
    RCC->CFGR &= 0xF8FF0000U;
    
    // 复位HSEON, CSSON和PLLON位
    RCC->CR &= 0xFEF6FFFFU;
    
    // 复位HSEBYP位
    RCC->CR &= 0xFFFBFFFFU;
    
    // 复位PLLSRC, PLLXTPRE, PLLMUL和USBPRE/OTGFSPRE位
    RCC->CFGR &= 0xFF80FFFFU;
    
    // 禁用所有中断并清除挂起位
    RCC->CIR = 0x009F0000U;
    
    // 配置向量表位置
#ifdef VECT_TAB_SRAM
    SCB->VTOR = SRAM_BASE | VECT_TAB_OFFSET; // 向量表重定位到内部SRAM
#else
    SCB->VTOR = FLASH_BASE | VECT_TAB_OFFSET; // 向量表保持在内部FLASH
#endif
}

void SystemCoreClockUpdate(void) {
    uint32_t tmp = 0U, pllmull = 0U, pllsource = 0U;
    
    // 获取SYSCLK源
    tmp = RCC->CFGR & RCC_CFGR_SWS;
    
    switch (tmp) {
        case 0x00U:  // HSI作为系统时钟
            SystemCoreClock = HSI_VALUE;
            break;
        case 0x04U:  // HSE作为系统时钟
            SystemCoreClock = HSE_VALUE;
            break;
        case 0x08U:  // PLL作为系统时钟
            // 获取PLL时钟源和倍频因子
            pllmull = RCC->CFGR & RCC_CFGR_PLLMULL;
            pllsource = RCC->CFGR & RCC_CFGR_PLLSRC;
            
            pllmull = ( pllmull >> 18U) + 2U; // 获取倍频因子
            
            if (pllsource == 0x00U) {
                // HSI振荡器时钟除以2选择作为PLL时钟输入
                SystemCoreClock = (HSI_VALUE >> 1U) * pllmull;
            } else {
                // HSE选择作为PLL时钟输入
                if ((RCC->CFGR & RCC_CFGR_PLLXTPRE) != (uint32_t)RESET) {
                    // HSE振荡器时钟除以2
                    SystemCoreClock = (HSE_VALUE >> 1U) * pllmull;
                } else {
                    SystemCoreClock = HSE_VALUE * pllmull;
                }
            }
            break;
        default:
            SystemCoreClock = HSI_VALUE;
            break;
    }
    
    // 计算HCLK时钟频率
    tmp = AHBPrescTable[((RCC->CFGR & RCC_CFGR_HPRE) >> 4U)];
    SystemCoreClock >>= tmp;
}
