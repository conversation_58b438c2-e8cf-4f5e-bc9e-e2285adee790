/* 中断服务程序 */
#include "main.h"
#include "stm32f1xx_it.h"

extern TIM_HandleTypeDef htim2;
extern UART_HandleTypeDef huart1;

void NMI_Handler(void) {
    // Non Maskable Interrupt
}

void HardFault_Handler(void) {
    while (1) {
        // 硬件错误处理
    }
}

void MemManage_Handler(void) {
    while (1) {
        // 内存管理错误
    }
}

void BusFault_Handler(void) {
    while (1) {
        // 总线错误
    }
}

void UsageFault_Handler(void) {
    while (1) {
        // 使用错误
    }
}

void SVC_Handler(void) {
    // 系统服务调用
}

void DebugMon_Handler(void) {
    // 调试监控
}

void PendSV_Handler(void) {
    // 可挂起系统调用
}

void SysTick_Handler(void) {
    HAL_IncTick(); // HAL库系统滴答
}

void TIM2_IRQHandler(void) {
    HAL_TIM_IRQHandler(&htim2); // 定时器2中断处理
}

void USART1_IRQHandler(void) {
    HAL_UART_IRQHandler(&huart1); // 串口1中断处理
}
