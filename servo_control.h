/* 舵机控制模块头文件 */
#ifndef __SERVO_CONTROL_H
#define __SERVO_CONTROL_H

#include "main.h"
#include "config.h"

// 舵机状态结构体
typedef struct {
    uint8_t servo_id; // 舵机ID
    uint16_t current_angle; // 当前角度
    uint16_t target_angle; // 目标角度
    uint16_t pulse_width; // 脉宽值
    uint8_t is_moving; // 是否正在移动
} Servo_TypeDef;

// 函数声明
void Servo_Init(void); // 舵机初始化
void Servo_SetAngle(uint8_t servo_id, uint16_t angle); // 设置舵机角度
void Servo_SetPulse(uint8_t servo_id, uint16_t pulse); // 设置舵机脉宽
uint16_t Servo_GetAngle(uint8_t servo_id); // 获取舵机当前角度
void Servo_SmoothMove(uint8_t servo_id, uint16_t target_angle, uint8_t speed); // 平滑移动
void Servo_Stop(uint8_t servo_id); // 停止舵机
void Servo_Home(void); // 回到初始位置
uint16_t Angle_To_Pulse(uint16_t angle); // 角度转脉宽
uint16_t Pulse_To_Angle(uint16_t pulse); // 脉宽转角度
void Process_Command(uint8_t cmd); // 处理串口命令
void Execute_Demo_Sequence(void); // 执行演示动作序列
void Move_To_Position(uint16_t base, uint16_t shoulder, uint16_t elbow, uint16_t wrist, uint16_t gripper); // 移动到指定位置

// 外部变量声明
extern Servo_TypeDef servos[SERVO_COUNT];

#endif /* __SERVO_CONTROL_H */
