Dependencies for Project 'RobotArm', Target 'RobotArm': (DO NOT MODIFY !)
F (.\main.c)(0x686DF8C4)(--c99 -c --cpu Cortex-M3 -g -O0 --apcs=interwork --split_sections -I .\ -I .\Drivers\STM32F1xx_HAL_Driver\Inc -I .\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy -I .\Drivers\CMSIS\Device\ST\STM32F1xx\Include -I .\Drivers\CMSIS\Include

-I.\RTE\_RobotArm

-IC:\Keil_v5\ARM\PACK\Keil\STM32F1xx_DFP\2.2.0\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="524" -DSTM32F10X_MD -DUSE_HAL_DRIVER -DSTM32F103xB

-o .\objects\main.o --omf_browse .\objects\main.crf --depend .\objects\main.d)
I (main.h)(0x686DF71C)
F (.\gimbal_control.c)(0x686DF90E)(--c99 -c --cpu Cortex-M3 -g -O0 --apcs=interwork --split_sections -I .\ -I .\Drivers\STM32F1xx_HAL_Driver\Inc -I .\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy -I .\Drivers\CMSIS\Device\ST\STM32F1xx\Include -I .\Drivers\CMSIS\Include

-I.\RTE\_RobotArm

-IC:\Keil_v5\ARM\PACK\Keil\STM32F1xx_DFP\2.2.0\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="524" -DSTM32F10X_MD -DUSE_HAL_DRIVER -DSTM32F103xB

-o .\objects\gimbal_control.o --omf_browse .\objects\gimbal_control.crf --depend .\objects\gimbal_control.d)
I (gimbal_control.h)(0x686DF777)
I (main.h)(0x686DF71C)
F (.\stm32f1xx_it.c)(0x686DF609)(--c99 -c --cpu Cortex-M3 -g -O0 --apcs=interwork --split_sections -I .\ -I .\Drivers\STM32F1xx_HAL_Driver\Inc -I .\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy -I .\Drivers\CMSIS\Device\ST\STM32F1xx\Include -I .\Drivers\CMSIS\Include

-I.\RTE\_RobotArm

-IC:\Keil_v5\ARM\PACK\Keil\STM32F1xx_DFP\2.2.0\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="524" -DSTM32F10X_MD -DUSE_HAL_DRIVER -DSTM32F103xB

-o .\objects\stm32f1xx_it.o --omf_browse .\objects\stm32f1xx_it.crf --depend .\objects\stm32f1xx_it.d)
I (main.h)(0x686DF71C)
F (.\system_stm32f1xx.c)(0x686DF68B)(--c99 -c --cpu Cortex-M3 -g -O0 --apcs=interwork --split_sections -I .\ -I .\Drivers\STM32F1xx_HAL_Driver\Inc -I .\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy -I .\Drivers\CMSIS\Device\ST\STM32F1xx\Include -I .\Drivers\CMSIS\Include

-I.\RTE\_RobotArm

-IC:\Keil_v5\ARM\PACK\Keil\STM32F1xx_DFP\2.2.0\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="524" -DSTM32F10X_MD -DUSE_HAL_DRIVER -DSTM32F103xB

-o .\objects\system_stm32f1xx.o --omf_browse .\objects\system_stm32f1xx.crf --depend .\objects\system_stm32f1xx.d)
F (.\startup_stm32f103xb.s)(0x686DF677)(--cpu Cortex-M3 -g --apcs=interwork 

-I.\RTE\_RobotArm

-IC:\Keil_v5\ARM\PACK\Keil\STM32F1xx_DFP\2.2.0\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

--pd "__UVISION_VERSION SETA 524" --pd "STM32F10X_MD SETA 1"

--list .\listings\startup_stm32f103xb.lst --xref -o .\objects\startup_stm32f103xb.o --depend .\objects\startup_stm32f103xb.d)
