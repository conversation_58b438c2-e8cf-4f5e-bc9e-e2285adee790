# STM32C8T6云台控制系统

## 项目简介
基于STM32C8T6微控制器的2轴云台控制系统，使用HAL库开发，支持PWM舵机控制、串口通信、多种工作模式和预设动作序列。

## 硬件配置

### 主控制器
- **MCU**: STM32F103C8T6 (72MHz, 64KB Flash, 20KB RAM)
- **开发环境**: Keil MDK-ARM 5
- **HAL库版本**: STM32Cube HAL F1

### 引脚配置
| 功能 | 引脚 | 说明 |
|------|------|------|
| 偏航轴舵机PWM | PA0 | TIM2_CH1 |
| 俯仰轴舵机PWM | PA1 | TIM2_CH2 |
| 串口发送 | PA9 | USART1_TX |
| 串口接收 | PA10 | USART1_RX |
| LED指示灯 | PC13 | GPIO输出 |
| 演示按键 | PB12 | GPIO输入(上拉) |

### 舵机连接
- **电源**: 5V/2A (推荐外部供电)
- **信号线**: 连接到对应PWM引脚
- **地线**: 共地连接
- **偏航轴**: 水平旋转舵机
- **俯仰轴**: 垂直旋转舵机

## 软件架构

### 文件结构
```
├── main.c              # 主程序文件
├── main.h              # 主程序头文件
├── config.h            # 系统配置文件
├── gimbal_control.c    # 云台控制模块
├── gimbal_control.h    # 云台控制头文件
├── stm32f1xx_it.c      # 中断服务程序
├── stm32f1xx_it.h      # 中断服务头文件
└── stm32f1xx_hal_conf.h # HAL库配置文件
```

### 核心模块
1. **主控制模块** (main.c)
   - 系统初始化
   - 主循环控制
   - 硬件配置

2. **云台控制模块** (gimbal_control.c)
   - PWM信号生成
   - 角度控制算法
   - 平滑运动控制
   - 多种工作模式
   - 预设动作序列

3. **配置管理** (config.h)
   - 系统参数配置
   - 硬件引脚定义
   - 运动参数设置

## 功能特性

### 基本控制
- **2轴控制**: 偏航轴(水平)、俯仰轴(垂直)
- **角度范围**: 偏航0-180度，俯仰30-150度
- **PWM频率**: 50Hz
- **脉宽范围**: 0.5ms-2.5ms

### 运动控制
- **平滑运动**: 支持速度可调的平滑移动
- **同步运动**: 双轴协调运动
- **位置记忆**: 预设位置快速切换
- **安全限位**: 角度范围保护

### 工作模式
- **手动模式**: 串口命令直接控制
- **跟踪模式**: 自动扫描跟踪
- **巡航模式**: 预设路径巡航

### 通信接口
- **串口控制**: 115200波特率
- **实时反馈**: 运动状态反馈
- **命令响应**: 即时命令执行确认

## 使用说明

### 串口命令
| 命令 | 功能 | 说明 |
|------|------|------|
| S | 停止 | 停止所有运动并切换到手动模式 |
| H | 回零 | 回到初始位置 |
| D | 演示 | 执行预设动作序列 |
| A | 偏航左转 | 偏航轴逆时针旋转 |
| E | 偏航右转 | 偏航轴顺时针旋转 |
| W | 俯仰向上 | 俯仰轴向上 |
| X | 俯仰向下 | 俯仰轴向下 |
| T | 跟踪模式 | 进入自动跟踪模式 |
| P | 巡航模式 | 进入巡航模式 |

### 按键操作
- **PB12按键**: 执行演示动作序列

### LED指示
- **PC13 LED**:
  - 常亮: 系统正常运行
  - 闪烁: 系统错误

### 工作模式详解
1. **手动模式**: 通过串口命令直接控制云台运动
2. **跟踪模式**: 云台自动左右扫描，模拟目标跟踪
3. **巡航模式**: 按预设路径巡航(中心->左->中心->右->中心->上->中心->下)

## 编译和下载

### Keil5配置
1. 新建工程，选择STM32F103C8
2. 添加HAL库文件
3. 配置包含路径
4. 设置编译选项
5. 配置下载器(ST-Link)

### 编译步骤
1. 打开Keil5
2. 导入项目文件
3. 检查库文件路径
4. 编译工程 (F7)
5. 下载程序 (F8)

## 调试和测试

### 串口调试
```bash
# 串口参数
波特率: 115200
数据位: 8
停止位: 1
校验位: 无
流控: 无
```

### 测试序列
1. 上电检查LED状态
2. 串口发送'H'回零测试
3. 串口发送'D'演示测试
4. 偏航和俯仰轴单独控制测试
5. 按键演示功能测试
6. 跟踪模式测试(发送'T')
7. 巡航模式测试(发送'P')

## 性能参数

### 系统性能
- **响应时间**: <50ms
- **控制精度**: ±1度
- **运动速度**: 可调(2-10度/步)
- **通信延迟**: <10ms

### 功耗参数
- **工作电流**: 50-200mA (不含舵机)
- **舵机电流**: 500-1500mA (负载相关)
- **待机功耗**: <10mA

## 扩展功能

### 可扩展特性
- 增加传感器反馈(陀螺仪、加速度计)
- 添加视觉识别(摄像头模块)
- 实现目标跟踪算法
- 支持无线控制(WiFi/蓝牙)
- 集成语音控制

### 硬件扩展
- 添加编码器反馈
- 增加IMU传感器
- 扩展I/O接口
- 添加OLED显示屏
- 集成摄像头模块

## 注意事项

### 安全提醒
- 首次使用前检查舵机连接
- 确保电源供电充足
- 避免云台碰撞障碍物
- 调试时注意人身安全
- 注意云台转动范围

### 维护建议
- 定期检查连接线路
- 清洁云台机械结构
- 更新软件版本
- 备份配置参数
- 定期校准云台零位

## 技术支持
- 开发环境: Keil MDK-ARM 5.x
- HAL库版本: STM32Cube_FW_F1_V1.8.0+
- 调试工具: ST-Link V2
- 串口工具: 任意串口调试助手

## 版本历史
- v1.0: 基础云台控制功能实现
- v1.1: 添加平滑运动控制
- v1.2: 优化串口通信
- v1.3: 增加演示动作序列
- v1.4: 添加跟踪和巡航模式
