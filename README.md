# STM32C8T6机械臂控制系统

## 项目简介
基于STM32C8T6微控制器的5自由度机械臂控制系统，使用HAL库开发，支持PWM舵机控制、串口通信和预设动作序列。

## 硬件配置

### 主控制器
- **MCU**: STM32F103C8T6 (72MHz, 64KB Flash, 20KB RAM)
- **开发环境**: Keil MDK-ARM 5
- **HAL库版本**: STM32Cube HAL F1

### 引脚配置
| 功能 | 引脚 | 说明 |
|------|------|------|
| 底座舵机PWM | PA0 | TIM2_CH1 |
| 肩部舵机PWM | PA1 | TIM2_CH2 |
| 肘部舵机PWM | PA2 | TIM2_CH3 |
| 腕部舵机PWM | PA3 | TIM2_CH4 |
| 夹爪舵机PWM | PA6 | TIM3_CH1 |
| 串口发送 | PA9 | USART1_TX |
| 串口接收 | PA10 | USART1_RX |
| LED指示灯 | PC13 | GPIO输出 |
| 演示按键 | PB12 | GPIO输入(上拉) |

### 舵机连接
- **电源**: 5V/2A (推荐外部供电)
- **信号线**: 连接到对应PWM引脚
- **地线**: 共地连接

## 软件架构

### 文件结构
```
├── main.c              # 主程序文件
├── main.h              # 主程序头文件
├── config.h            # 系统配置文件
├── servo_control.c     # 舵机控制模块
├── servo_control.h     # 舵机控制头文件
├── stm32f1xx_it.c      # 中断服务程序
├── stm32f1xx_it.h      # 中断服务头文件
└── stm32f1xx_hal_conf.h # HAL库配置文件
```

### 核心模块
1. **主控制模块** (main.c)
   - 系统初始化
   - 主循环控制
   - 硬件配置

2. **舵机控制模块** (servo_control.c)
   - PWM信号生成
   - 角度控制算法
   - 平滑运动控制
   - 预设动作序列

3. **配置管理** (config.h)
   - 系统参数配置
   - 硬件引脚定义
   - 运动参数设置

## 功能特性

### 基本控制
- **5自由度控制**: 底座、肩部、肘部、腕部、夹爪
- **角度范围**: 0-180度 (可配置)
- **PWM频率**: 50Hz
- **脉宽范围**: 0.5ms-2.5ms

### 运动控制
- **平滑运动**: 支持速度可调的平滑移动
- **同步运动**: 多关节协调运动
- **位置记忆**: 预设位置快速切换
- **安全限位**: 角度范围保护

### 通信接口
- **串口控制**: 115200波特率
- **实时反馈**: 运动状态反馈
- **命令响应**: 即时命令执行确认

## 使用说明

### 串口命令
| 命令 | 功能 | 说明 |
|------|------|------|
| S | 停止 | 停止所有舵机运动 |
| H | 回零 | 回到初始位置 |
| D | 演示 | 执行预设动作序列 |
| Q | 底座左转 | 底座逆时针旋转 |
| E | 底座右转 | 底座顺时针旋转 |
| W | 肩部上抬 | 肩部关节向上 |
| X | 肩部下压 | 肩部关节向下 |
| A | 肘部上抬 | 肘部关节向上 |
| Z | 肘部下压 | 肘部关节向下 |
| R | 腕部上抬 | 腕部关节向上 |
| F | 腕部下压 | 腕部关节向下 |
| T | 夹爪张开 | 夹爪打开 |
| G | 夹爪闭合 | 夹爪闭合 |

### 按键操作
- **PB12按键**: 执行演示动作序列

### LED指示
- **PC13 LED**: 
  - 常亮: 系统正常运行
  - 闪烁: 系统错误

## 编译和下载

### Keil5配置
1. 新建工程，选择STM32F103C8
2. 添加HAL库文件
3. 配置包含路径
4. 设置编译选项
5. 配置下载器(ST-Link)

### 编译步骤
1. 打开Keil5
2. 导入项目文件
3. 检查库文件路径
4. 编译工程 (F7)
5. 下载程序 (F8)

## 调试和测试

### 串口调试
```bash
# 串口参数
波特率: 115200
数据位: 8
停止位: 1
校验位: 无
流控: 无
```

### 测试序列
1. 上电检查LED状态
2. 串口发送'H'回零测试
3. 串口发送'D'演示测试
4. 各关节单独控制测试
5. 按键演示功能测试

## 性能参数

### 系统性能
- **响应时间**: <50ms
- **控制精度**: ±1度
- **运动速度**: 可调(5-20度/步)
- **通信延迟**: <10ms

### 功耗参数
- **工作电流**: 50-200mA (不含舵机)
- **舵机电流**: 500-1500mA (负载相关)
- **待机功耗**: <10mA

## 扩展功能

### 可扩展特性
- 增加传感器反馈
- 添加视觉识别
- 实现路径规划
- 支持无线控制
- 集成语音控制

### 硬件扩展
- 添加编码器反馈
- 增加力传感器
- 扩展I/O接口
- 添加显示屏

## 注意事项

### 安全提醒
- 首次使用前检查舵机连接
- 确保电源供电充足
- 避免机械臂碰撞障碍物
- 调试时注意人身安全

### 维护建议
- 定期检查连接线路
- 清洁机械结构
- 更新软件版本
- 备份配置参数

## 技术支持
- 开发环境: Keil MDK-ARM 5.x
- HAL库版本: STM32Cube_FW_F1_V1.8.0+
- 调试工具: ST-Link V2
- 串口工具: 任意串口调试助手

## 版本历史
- v1.0: 基础功能实现
- v1.1: 添加平滑运动控制
- v1.2: 优化串口通信
- v1.3: 增加演示动作序列
