# STM32 HAL库缺失问题快速解决方案

## 🚨 问题描述
```
main.h(9): error: #5: cannot open source input file "stm32f1xx_hal.h": No such file or directory
```

## ⚡ 快速解决方案

### 方案1：安装HAL库（推荐）

#### 步骤1：打开Pack Installer
1. 在Keil5中按 `Ctrl+P` 或点击菜单 `Project` -> `Manage` -> `Pack Installer`

#### 步骤2：安装STM32F1包
1. 左侧选择 `STMicroelectronics` -> `STM32F1 Series` -> `STM32F103C8`
2. 右侧找到 `Keil::STM32F1xx_DFP` 点击 `Install`
3. 等待下载安装完成

#### 步骤3：重新编译
1. 关闭Pack Installer
2. 重新编译项目

### 方案2：使用标准库版本（临时方案）

如果无法安装HAL库，可以使用我提供的 `main_stdlib.c` 文件：

1. 将 `main_stdlib.c` 重命名为 `main.c` 替换原文件
2. 在Keil5项目中移除HAL库相关文件
3. 添加STM32标准库文件

### 方案3：手动下载HAL库

#### 下载地址
- 官方下载：https://www.st.com/en/embedded-software/stm32cubef1.html
- Keil包下载：https://www.keil.com/dd2/pack/

#### 安装步骤
1. 下载 `STM32Cube_FW_F1_Vx.x.x.zip`
2. 解压到本地目录
3. 在Keil项目中添加包含路径：
   ```
   解压路径\Drivers\STM32F1xx_HAL_Driver\Inc
   解压路径\Drivers\CMSIS\Device\ST\STM32F1xx\Include
   解压路径\Drivers\CMSIS\Include
   ```

## 🔧 项目配置检查

### 检查包含路径
在Keil5项目设置中确保包含以下路径：
```
.\
.\Drivers\STM32F1xx_HAL_Driver\Inc
.\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy
.\Drivers\CMSIS\Device\ST\STM32F1xx\Include
.\Drivers\CMSIS\Include
```

### 检查预定义宏
确保定义了以下宏：
```
USE_HAL_DRIVER
STM32F103xB
```

## 📋 验证安装成功

安装成功后应该能找到以下文件：
```
C:\Keil_v5\ARM\PACK\Keil\STM32F1xx_DFP\版本号\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h
```

## 🆘 如果仍然有问题

1. **检查Keil版本**：建议使用Keil5.25或更高版本
2. **检查网络**：确保能正常访问互联网
3. **管理员权限**：以管理员身份运行Keil5
4. **防火墙设置**：确保防火墙不阻止Keil下载

## 📞 联系支持

如果以上方案都无法解决，请：
1. 检查Keil5安装是否完整
2. 重新安装Keil5
3. 使用STM32CubeMX生成项目代码

## 🎯 推荐做法

为了避免类似问题，建议：
1. 使用STM32CubeMX生成初始项目
2. 定期更新Keil Pack
3. 保持Keil5版本更新
