/* 云台控制模块头文件 */
#ifndef __GIMBAL_CONTROL_H
#define __GIMBAL_CONTROL_H

#include "main.h"
#include "config.h"

// 云台状态结构体
typedef struct {
    uint8_t axis_id; // 轴ID
    uint16_t current_angle; // 当前角度
    uint16_t target_angle; // 目标角度
    uint16_t pulse_width; // 脉宽值
    uint8_t is_moving; // 是否正在移动
} Gimbal_TypeDef;

// 云台模式枚举
typedef enum {
    GIMBAL_MODE_MANUAL = 0, // 手动模式
    GIMBAL_MODE_TRACK,      // 跟踪模式
    GIMBAL_MODE_PATROL      // 巡航模式
} Gimbal_Mode_t;

// 函数声明
void Gimbal_Init(void); // 云台初始化
void Gimbal_SetAngle(uint8_t axis_id, uint16_t angle); // 设置云台角度
void Gimbal_SetPulse(uint8_t axis_id, uint16_t pulse); // 设置云台脉宽
uint16_t Gimbal_GetAngle(uint8_t axis_id); // 获取云台当前角度
void Gimbal_SmoothMove(uint8_t axis_id, uint16_t target_angle, uint8_t speed); // 平滑移动
void Gimbal_Stop(uint8_t axis_id); // 停止云台
void Gimbal_Home(void); // 回到初始位置
uint16_t Angle_To_Pulse(uint16_t angle); // 角度转脉宽
uint16_t Pulse_To_Angle(uint16_t pulse); // 脉宽转角度
void Process_Command(uint8_t cmd); // 处理串口命令
void Execute_Demo_Sequence(void); // 执行演示动作序列
void Gimbal_Move_To_Position(uint16_t yaw, uint16_t pitch); // 移动到指定位置
void Gimbal_Patrol_Mode(void); // 巡航模式
void Gimbal_Track_Mode(void); // 跟踪模式

// 外部变量声明
extern Gimbal_TypeDef gimbal[SERVO_COUNT];
extern Gimbal_Mode_t gimbal_mode;

#endif /* __GIMBAL_CONTROL_H */
