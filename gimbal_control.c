/* 云台控制模块实现 */
#include "gimbal_control.h"

Gimbal_TypeDef gimbal[SERVO_COUNT]; // 云台状态数组
Gimbal_Mode_t gimbal_mode = GIMBAL_MODE_MANUAL; // 当前模式

// PWM通道映射表
uint32_t pwm_channels[SERVO_COUNT] = {
    TIM_CHANNEL_1, // 偏航轴
    TIM_CHANNEL_2  // 俯仰轴
};

void Gimbal_Init(void) {
    // 初始化云台状态
    for(int i = 0; i < SERVO_COUNT; i++) {
        gimbal[i].axis_id = i;
        gimbal[i].current_angle = 90; // 初始角度90度
        gimbal[i].target_angle = 90;
        gimbal[i].pulse_width = SERVO_CENTER_PULSE;
        gimbal[i].is_moving = 0;
    }
    
    // 启动PWM输出
    HAL_TIM_PWM_Start(&htim2, TIM_CHANNEL_1);
    HAL_TIM_PWM_Start(&htim2, TIM_CHANNEL_2);
    
    // 设置初始脉宽
    for(int i = 0; i < SERVO_COUNT; i++) {
        __HAL_TIM_SET_COMPARE(&htim2, pwm_channels[i], SERVO_CENTER_PULSE);
    }
    
    gimbal_mode = GIMBAL_MODE_MANUAL; // 默认手动模式
}

uint16_t Angle_To_Pulse(uint16_t angle) {
    if(angle > SERVO_MAX_ANGLE) angle = SERVO_MAX_ANGLE;
    if(angle < SERVO_MIN_ANGLE) angle = SERVO_MIN_ANGLE;
    
    // 线性映射: 0-180度 -> 500-2500us
    return SERVO_MIN_PULSE + (angle * (SERVO_MAX_PULSE - SERVO_MIN_PULSE)) / SERVO_MAX_ANGLE;
}

uint16_t Pulse_To_Angle(uint16_t pulse) {
    if(pulse > SERVO_MAX_PULSE) pulse = SERVO_MAX_PULSE;
    if(pulse < SERVO_MIN_PULSE) pulse = SERVO_MIN_PULSE;
    
    return ((pulse - SERVO_MIN_PULSE) * SERVO_MAX_ANGLE) / (SERVO_MAX_PULSE - SERVO_MIN_PULSE);
}

void Gimbal_SetAngle(uint8_t axis_id, uint16_t angle) {
    if(axis_id >= SERVO_COUNT) return;
    
    // 角度限制检查
    if(axis_id == GIMBAL_YAW) {
        if(angle < YAW_MIN_ANGLE) angle = YAW_MIN_ANGLE;
        if(angle > YAW_MAX_ANGLE) angle = YAW_MAX_ANGLE;
    } else if(axis_id == GIMBAL_PITCH) {
        if(angle < PITCH_MIN_ANGLE) angle = PITCH_MIN_ANGLE;
        if(angle > PITCH_MAX_ANGLE) angle = PITCH_MAX_ANGLE;
    }
    
    uint16_t pulse = Angle_To_Pulse(angle);
    gimbal[axis_id].current_angle = angle;
    gimbal[axis_id].target_angle = angle;
    gimbal[axis_id].pulse_width = pulse;
    
    __HAL_TIM_SET_COMPARE(&htim2, pwm_channels[axis_id], pulse);
}

void Gimbal_SetPulse(uint8_t axis_id, uint16_t pulse) {
    if(axis_id >= SERVO_COUNT) return;
    
    gimbal[axis_id].pulse_width = pulse;
    gimbal[axis_id].current_angle = Pulse_To_Angle(pulse);
    
    __HAL_TIM_SET_COMPARE(&htim2, pwm_channels[axis_id], pulse);
}

uint16_t Gimbal_GetAngle(uint8_t axis_id) {
    if(axis_id >= SERVO_COUNT) return 0;
    return gimbal[axis_id].current_angle;
}

void Gimbal_SmoothMove(uint8_t axis_id, uint16_t target_angle, uint8_t speed) {
    if(axis_id >= SERVO_COUNT) return;
    
    // 角度限制检查
    if(axis_id == GIMBAL_YAW) {
        if(target_angle < YAW_MIN_ANGLE) target_angle = YAW_MIN_ANGLE;
        if(target_angle > YAW_MAX_ANGLE) target_angle = YAW_MAX_ANGLE;
    } else if(axis_id == GIMBAL_PITCH) {
        if(target_angle < PITCH_MIN_ANGLE) target_angle = PITCH_MIN_ANGLE;
        if(target_angle > PITCH_MAX_ANGLE) target_angle = PITCH_MAX_ANGLE;
    }
    
    gimbal[axis_id].target_angle = target_angle;
    gimbal[axis_id].is_moving = 1;
    
    uint16_t current = gimbal[axis_id].current_angle;
    
    while(current != target_angle && gimbal[axis_id].is_moving) {
        if(current < target_angle) {
            current += (current + speed <= target_angle) ? speed : (target_angle - current);
        } else {
            current -= (current - speed >= target_angle) ? speed : (current - target_angle);
        }
        
        Gimbal_SetAngle(axis_id, current);
        HAL_Delay(MOVE_DELAY_MS);
    }
    
    gimbal[axis_id].is_moving = 0;
}

void Gimbal_Stop(uint8_t axis_id) {
    if(axis_id >= SERVO_COUNT) return;
    gimbal[axis_id].is_moving = 0;
}

void Gimbal_Home(void) {
    // 平滑移动到初始位置
    Gimbal_SmoothMove(GIMBAL_YAW, POS_HOME_YAW, MOVE_SPEED_NORMAL);
    Gimbal_SmoothMove(GIMBAL_PITCH, POS_HOME_PITCH, MOVE_SPEED_NORMAL);
}

void Gimbal_Move_To_Position(uint16_t yaw, uint16_t pitch) {
    // 同时移动偏航和俯仰轴到指定位置
    gimbal[GIMBAL_YAW].target_angle = yaw;
    gimbal[GIMBAL_PITCH].target_angle = pitch;
    
    uint8_t all_reached = 0;
    while(!all_reached) {
        all_reached = 1;
        
        for(int i = 0; i < SERVO_COUNT; i++) {
            uint16_t current = gimbal[i].current_angle;
            uint16_t target = gimbal[i].target_angle;
            
            if(current != target) {
                all_reached = 0;
                if(current < target) {
                    current += (current + MOVE_SPEED_NORMAL <= target) ? MOVE_SPEED_NORMAL : (target - current);
                } else {
                    current -= (current - MOVE_SPEED_NORMAL >= target) ? MOVE_SPEED_NORMAL : (current - target);
                }
                Gimbal_SetAngle(i, current);
            }
        }
        HAL_Delay(MOVE_DELAY_MS);
    }
}

void Gimbal_Patrol_Mode(void) {
    gimbal_mode = GIMBAL_MODE_PATROL;
    
    // 巡航路径: 中心->左->中心->右->中心->上->中心->下
    uint16_t patrol_positions[][2] = {
        {POS_HOME_YAW, POS_HOME_PITCH},         // 中心
        {POS_LOOK_LEFT_YAW, POS_LOOK_LEFT_PITCH},   // 左
        {POS_HOME_YAW, POS_HOME_PITCH},         // 中心
        {POS_LOOK_RIGHT_YAW, POS_LOOK_RIGHT_PITCH}, // 右
        {POS_HOME_YAW, POS_HOME_PITCH},         // 中心
        {POS_LOOK_UP_YAW, POS_LOOK_UP_PITCH},   // 上
        {POS_HOME_YAW, POS_HOME_PITCH},         // 中心
        {POS_LOOK_DOWN_YAW, POS_LOOK_DOWN_PITCH} // 下
    };
    
    for(int i = 0; i < 8 && gimbal_mode == GIMBAL_MODE_PATROL; i++) {
        Gimbal_Move_To_Position(patrol_positions[i][0], patrol_positions[i][1]);
        HAL_Delay(PATROL_DELAY);
    }
}

void Gimbal_Track_Mode(void) {
    gimbal_mode = GIMBAL_MODE_TRACK;
    // 跟踪模式实现(可根据传感器数据调整)
    // 这里实现简单的扫描跟踪
    uint16_t scan_yaw = POS_LOOK_LEFT_YAW;
    uint8_t direction = 1; // 1:右扫 0:左扫
    
    while(gimbal_mode == GIMBAL_MODE_TRACK) {
        Gimbal_SetAngle(GIMBAL_YAW, scan_yaw);
        
        if(direction) {
            scan_yaw += TRACK_SPEED;
            if(scan_yaw >= POS_LOOK_RIGHT_YAW) direction = 0;
        } else {
            scan_yaw -= TRACK_SPEED;
            if(scan_yaw <= POS_LOOK_LEFT_YAW) direction = 1;
        }
        
        HAL_Delay(100);
    }
}

void Process_Command(uint8_t cmd) {
    char response[50];
    
    switch(cmd) {
        case CMD_STOP:
            gimbal_mode = GIMBAL_MODE_MANUAL;
            for(int i = 0; i < SERVO_COUNT; i++) Gimbal_Stop(i);
            sprintf(response, "云台停止\r\n");
            break;
            
        case CMD_HOME:
            gimbal_mode = GIMBAL_MODE_MANUAL;
            Gimbal_Home();
            sprintf(response, "回到初始位置\r\n");
            break;
            
        case CMD_DEMO:
            Execute_Demo_Sequence();
            sprintf(response, "演示动作完成\r\n");
            break;
            
        case CMD_YAW_LEFT:
            gimbal_mode = GIMBAL_MODE_MANUAL;
            Gimbal_SmoothMove(GIMBAL_YAW, Gimbal_GetAngle(GIMBAL_YAW) - MOVE_SPEED_FAST, MOVE_SPEED_FAST);
            sprintf(response, "偏航左转\r\n");
            break;
            
        case CMD_YAW_RIGHT:
            gimbal_mode = GIMBAL_MODE_MANUAL;
            Gimbal_SmoothMove(GIMBAL_YAW, Gimbal_GetAngle(GIMBAL_YAW) + MOVE_SPEED_FAST, MOVE_SPEED_FAST);
            sprintf(response, "偏航右转\r\n");
            break;
            
        case CMD_PITCH_UP:
            gimbal_mode = GIMBAL_MODE_MANUAL;
            Gimbal_SmoothMove(GIMBAL_PITCH, Gimbal_GetAngle(GIMBAL_PITCH) - MOVE_SPEED_FAST, MOVE_SPEED_FAST);
            sprintf(response, "俯仰向上\r\n");
            break;
            
        case CMD_PITCH_DOWN:
            gimbal_mode = GIMBAL_MODE_MANUAL;
            Gimbal_SmoothMove(GIMBAL_PITCH, Gimbal_GetAngle(GIMBAL_PITCH) + MOVE_SPEED_FAST, MOVE_SPEED_FAST);
            sprintf(response, "俯仰向下\r\n");
            break;
            
        case CMD_TRACK_MODE:
            Gimbal_Track_Mode();
            sprintf(response, "进入跟踪模式\r\n");
            break;
            
        case CMD_PATROL_MODE:
            Gimbal_Patrol_Mode();
            sprintf(response, "进入巡航模式\r\n");
            break;
            
        default:
            sprintf(response, "未知命令: %c\r\n", cmd);
            break;
    }
    
    HAL_UART_Transmit(&huart1, (uint8_t*)response, strlen(response), HAL_MAX_DELAY);
}

void Execute_Demo_Sequence(void) {
    char msg[] = "开始云台演示动作序列\r\n";
    HAL_UART_Transmit(&huart1, (uint8_t*)msg, strlen(msg), HAL_MAX_DELAY);
    
    gimbal_mode = GIMBAL_MODE_MANUAL;
    
    // 1. 回到初始位置
    Gimbal_Move_To_Position(POS_HOME_YAW, POS_HOME_PITCH);
    HAL_Delay(1000);
    
    // 2. 左视
    Gimbal_Move_To_Position(POS_LOOK_LEFT_YAW, POS_LOOK_LEFT_PITCH);
    HAL_Delay(1000);
    
    // 3. 右视
    Gimbal_Move_To_Position(POS_LOOK_RIGHT_YAW, POS_LOOK_RIGHT_PITCH);
    HAL_Delay(1000);
    
    // 4. 上视
    Gimbal_Move_To_Position(POS_LOOK_UP_YAW, POS_LOOK_UP_PITCH);
    HAL_Delay(1000);
    
    // 5. 下视
    Gimbal_Move_To_Position(POS_LOOK_DOWN_YAW, POS_LOOK_DOWN_PITCH);
    HAL_Delay(1000);
    
    // 6. 回到初始位置
    Gimbal_Move_To_Position(POS_HOME_YAW, POS_HOME_PITCH);
    
    char complete_msg[] = "云台演示动作序列完成\r\n";
    HAL_UART_Transmit(&huart1, (uint8_t*)complete_msg, strlen(complete_msg), HAL_MAX_DELAY);
}
