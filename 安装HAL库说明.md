# STM32 HAL库安装说明

## 方法1：使用Keil Pack Installer（推荐）

### 步骤1：打开Pack Installer
1. 打开Keil5
2. 点击菜单栏 `Project` -> `Manage` -> `Pack Installer`
3. 或者直接点击工具栏的Pack Installer图标

### 步骤2：安装STM32F1xx包
1. 在左侧设备列表中找到 `STMicroelectronics`
2. 展开后找到 `STM32F1 Series`
3. 选择 `STM32F103C8`
4. 在右侧Packs列表中找到 `Keil::STM32F1xx_DFP`
5. 点击 `Install` 按钮进行安装

### 步骤3：验证安装
1. 安装完成后关闭Pack Installer
2. 重新打开Keil5
3. 新建工程时应该能看到STM32F103C8芯片

## 方法2：手动下载安装包

### 下载地址
- STM32F1xx Device Family Pack: https://www.keil.com/dd2/pack/
- 搜索 "STM32F1xx" 下载最新版本

### 安装步骤
1. 下载 `.pack` 文件
2. 双击 `.pack` 文件自动安装
3. 重启Keil5

## 方法3：使用STM32CubeMX生成项目

### 步骤
1. 下载安装STM32CubeMX
2. 选择STM32F103C8芯片
3. 配置引脚和时钟
4. 生成Keil5项目代码
5. 自动包含HAL库文件

## 验证HAL库安装成功

安装成功后，以下路径应该存在HAL库文件：
```
C:\Keil_v5\ARM\PACK\Keil\STM32F1xx_DFP\版本号\Drivers\STM32F1xx_HAL_Driver\
```

## 如果仍然有问题

请检查：
1. Keil5版本是否支持（建议5.25以上）
2. 网络连接是否正常
3. 防火墙是否阻止下载
4. 是否有管理员权限
